﻿{
    "TestSummary":  {
                        "PassedTests":  14,
                        "TotalTests":  18,
                        "FailedTests":  4,
                        "BaseUrl":  "http://localhost:8080/api/v1",
                        "TestDate":  {
                                         "value":  "\/Date(1753840430666)\/",
                                         "DisplayHint":  2,
                                         "DateTime":  "2025年7月30日 9:53:50"
                                     },
                        "SuccessRate":  77.78
                    },
    "TestResults":  [
                        {
                            "Success":  true,
                            "Message":  "Successfully retrieved subscription plans",
                            "StatusCode":  200,
                            "TestName":  "Get Subscription Plans",
                            "Method":  "GET",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840429377)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:49"
                                          },
                            "Endpoint":  "/plans"
                        },
                        {
                            "Success":  false,
                            "Message":  "",
                            "StatusCode":  401,
                            "TestName":  "Get Specific Plan",
                            "Method":  "GET",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840429431)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:49"
                                          },
                            "Endpoint":  "/plans/1"
                        },
                        {
                            "Success":  true,
                            "Message":  "User registered successfully",
                            "StatusCode":  200,
                            "TestName":  "User Registration",
                            "Method":  "POST",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840429701)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:49"
                                          },
                            "Endpoint":  "/auth/register"
                        },
                        {
                            "Success":  true,
                            "Message":  "User logged in successfully",
                            "StatusCode":  200,
                            "TestName":  "User Login",
                            "Method":  "POST",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840429879)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:49"
                                          },
                            "Endpoint":  "/auth/login"
                        },
                        {
                            "Success":  true,
                            "Message":  "Token validated successfully",
                            "StatusCode":  200,
                            "TestName":  "Token Validation",
                            "Method":  "GET",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840429896)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:49"
                                          },
                            "Endpoint":  "/auth/validate"
                        },
                        {
                            "Success":  true,
                            "Message":  "User profile retrieved successfully",
                            "StatusCode":  200,
                            "TestName":  "Get User Profile",
                            "Method":  "GET",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840429924)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:49"
                                          },
                            "Endpoint":  "/users/profile"
                        },
                        {
                            "Success":  false,
                            "Message":  "系统内部错误，请联系管理员",
                            "StatusCode":  500,
                            "TestName":  "Update User Profile",
                            "Method":  "PUT",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840429970)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:49"
                                          },
                            "Endpoint":  "/users/profile"
                        },
                        {
                            "Success":  true,
                            "Message":  "Password validated successfully",
                            "StatusCode":  200,
                            "TestName":  "Validate Password",
                            "Method":  "POST",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840430141)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:50"
                                          },
                            "Endpoint":  "/users/validate-password"
                        },
                        {
                            "Success":  true,
                            "Message":  "Personal stats retrieved successfully",
                            "StatusCode":  200,
                            "TestName":  "Get Personal Stats",
                            "Method":  "GET",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840430159)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:50"
                                          },
                            "Endpoint":  "/users/personal-stats"
                        },
                        {
                            "Success":  true,
                            "Message":  "Profile detail retrieved successfully",
                            "StatusCode":  200,
                            "TestName":  "Get Profile Detail",
                            "Method":  "GET",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840430181)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:50"
                                          },
                            "Endpoint":  "/users/profile-detail"
                        },
                        {
                            "Success":  true,
                            "Message":  "Team created successfully",
                            "StatusCode":  200,
                            "TestName":  "Create Team",
                            "Method":  "POST",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840430245)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:50"
                                          },
                            "Endpoint":  "/teams"
                        },
                        {
                            "Success":  true,
                            "Message":  "Teams list retrieved successfully",
                            "StatusCode":  200,
                            "TestName":  "Get Teams List",
                            "Method":  "GET",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840430279)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:50"
                                          },
                            "Endpoint":  "/teams"
                        },
                        {
                            "Success":  true,
                            "Message":  "User subscriptions retrieved successfully",
                            "StatusCode":  200,
                            "TestName":  "Get User Subscriptions",
                            "Method":  "GET",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840430305)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:50"
                                          },
                            "Endpoint":  "/subscriptions"
                        },
                        {
                            "Success":  true,
                            "Message":  "Current subscription retrieved successfully",
                            "StatusCode":  200,
                            "TestName":  "Get Current Subscription",
                            "Method":  "GET",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840430324)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:50"
                                          },
                            "Endpoint":  "/subscriptions/current"
                        },
                        {
                            "Success":  false,
                            "Message":  "系统内部错误，请联系管理员",
                            "StatusCode":  500,
                            "TestName":  "Create Subscription",
                            "Method":  "POST",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840430363)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:50"
                                          },
                            "Endpoint":  "/subscriptions"
                        },
                        {
                            "Success":  true,
                            "Message":  "TODOs retrieved successfully",
                            "StatusCode":  200,
                            "TestName":  "Get TODOs",
                            "Method":  "GET",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840430389)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:50"
                                          },
                            "Endpoint":  "/todos"
                        },
                        {
                            "Success":  false,
                            "Message":  "系统内部错误，请联系管理员",
                            "StatusCode":  500,
                            "TestName":  "Create TODO",
                            "Method":  "POST",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840430483)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:50"
                                          },
                            "Endpoint":  "/todos"
                        },
                        {
                            "Success":  true,
                            "Message":  "User logged out successfully",
                            "StatusCode":  200,
                            "TestName":  "User Logout",
                            "Method":  "POST",
                            "Timestamp":  {
                                              "value":  "\/Date(1753840430523)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "2025年7月30日 9:53:50"
                                          },
                            "Endpoint":  "/auth/logout"
                        }
                    ]
}
