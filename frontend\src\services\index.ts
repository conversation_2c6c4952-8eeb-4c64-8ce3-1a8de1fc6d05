/**
 * 服务层统一入口
 */

// 导出类型定义
export * from '@/types/api';
// 导出请求工具
export { apiRequest, TokenManager } from '@/utils/request';
// 导出所有服务
// 导出默认服务实例
export { AuthService, default as authService } from './auth';
export {
  default as subscriptionService,
  SubscriptionService,
} from './subscription';
export { default as teamService, TeamService } from './team';
export { default as userService, UserService } from './user';
