# API Endpoint Testing Script for Team Management System
# This script tests all backend API endpoints to verify functionality

param(
    [string]$BaseUrl = "http://localhost:8080/api/v1",
    [switch]$Verbose = $false
)

# Global variables
$Global:TestResults = @()
$Global:AuthToken = $null
$Global:TestUser = @{
    email = "test.user.$(Get-Random)@example.com"
    password = "TestPassword123!"
    name = "Test User $(Get-Random)"
}

# Helper function to make HTTP requests
function Invoke-ApiRequest {
    param(
        [string]$Method,
        [string]$Endpoint,
        [hashtable]$Body = $null,
        [hashtable]$Headers = @{},
        [bool]$RequireAuth = $false
    )
    
    $Uri = "$BaseUrl$Endpoint"
    $RequestHeaders = $Headers.Clone()
    
    if ($RequireAuth -and $Global:AuthToken) {
        $RequestHeaders["Authorization"] = "Bearer $Global:AuthToken"
    }
    
    $RequestHeaders["Content-Type"] = "application/json"
    
    try {
        $params = @{
            Uri = $Uri
            Method = $Method
            Headers = $RequestHeaders
            UseBasicParsing = $true
        }
        
        if ($Body) {
            $params.Body = ($Body | ConvertTo-Json -Depth 10)
        }
        
        if ($Verbose) {
            Write-Host "Making $Method request to: $Uri" -ForegroundColor Cyan
            if ($Body) {
                Write-Host "Request Body: $($params.Body)" -ForegroundColor Gray
            }
        }
        
        $response = Invoke-RestMethod @params
        return @{
            Success = $true
            StatusCode = 200
            Data = $response
            Error = $null
        }
    }
    catch {
        $statusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
        $errorMessage = $_.Exception.Message
        
        if ($_.Exception.Response) {
            try {
                $errorStream = $_.Exception.Response.GetResponseStream()
                $reader = New-Object System.IO.StreamReader($errorStream)
                $errorBody = $reader.ReadToEnd()
                $errorData = $errorBody | ConvertFrom-Json
                $errorMessage = $errorData.message
            }
            catch {
                # If we can't parse the error response, use the original message
            }
        }
        
        return @{
            Success = $false
            StatusCode = $statusCode
            Data = $null
            Error = $errorMessage
        }
    }
}

# Function to log test results
function Add-TestResult {
    param(
        [string]$TestName,
        [string]$Endpoint,
        [string]$Method,
        [bool]$Success,
        [string]$Message,
        [int]$StatusCode = 0
    )
    
    $result = @{
        TestName = $TestName
        Endpoint = $Endpoint
        Method = $Method
        Success = $Success
        Message = $Message
        StatusCode = $StatusCode
        Timestamp = Get-Date
    }
    
    $Global:TestResults += $result
    
    $status = if ($Success) { "PASS" } else { "FAIL" }
    $color = if ($Success) { "Green" } else { "Red" }
    
    Write-Host "[$status] $TestName - $Message" -ForegroundColor $color
    if ($Verbose -and !$Success) {
        Write-Host "  Status Code: $StatusCode" -ForegroundColor Yellow
    }
}

# Test functions
function Test-PublicEndpoints {
    Write-Host "`n=== Testing Public Endpoints ===" -ForegroundColor Yellow
    
    # Test subscription plans endpoint
    $response = Invoke-ApiRequest -Method "GET" -Endpoint "/plans"
    if ($response.Success) {
        Add-TestResult -TestName "Get Subscription Plans" -Endpoint "/plans" -Method "GET" -Success $true -Message "Successfully retrieved subscription plans" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "Get Subscription Plans" -Endpoint "/plans" -Method "GET" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }
    
    # Test specific plan endpoint (assuming plan ID 1 exists)
    $response = Invoke-ApiRequest -Method "GET" -Endpoint "/plans/1"
    if ($response.Success) {
        Add-TestResult -TestName "Get Specific Plan" -Endpoint "/plans/1" -Method "GET" -Success $true -Message "Successfully retrieved specific plan" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "Get Specific Plan" -Endpoint "/plans/1" -Method "GET" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }
}

function Test-AuthenticationFlow {
    Write-Host "`n=== Testing Authentication Flow ===" -ForegroundColor Yellow
    
    # Test user registration
    $registerBody = @{
        email = $Global:TestUser.email
        password = $Global:TestUser.password
        name = $Global:TestUser.name
    }
    
    $response = Invoke-ApiRequest -Method "POST" -Endpoint "/auth/register" -Body $registerBody
    if ($response.Success) {
        Add-TestResult -TestName "User Registration" -Endpoint "/auth/register" -Method "POST" -Success $true -Message "User registered successfully" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "User Registration" -Endpoint "/auth/register" -Method "POST" -Success $false -Message $response.Error -StatusCode $response.StatusCode
        return $false
    }
    
    # Test user login
    $loginBody = @{
        email = $Global:TestUser.email
        password = $Global:TestUser.password
    }
    
    $response = Invoke-ApiRequest -Method "POST" -Endpoint "/auth/login" -Body $loginBody
    if ($response.Success -and $response.Data.data.token) {
        $Global:AuthToken = $response.Data.data.token
        Add-TestResult -TestName "User Login" -Endpoint "/auth/login" -Method "POST" -Success $true -Message "User logged in successfully" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "User Login" -Endpoint "/auth/login" -Method "POST" -Success $false -Message $response.Error -StatusCode $response.StatusCode
        return $false
    }
    
    # Test token validation
    $response = Invoke-ApiRequest -Method "GET" -Endpoint "/auth/validate" -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "Token Validation" -Endpoint "/auth/validate" -Method "GET" -Success $true -Message "Token validated successfully" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "Token Validation" -Endpoint "/auth/validate" -Method "GET" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }
    
    return $true
}

function Test-UserEndpoints {
    Write-Host "`n=== Testing User Management Endpoints ===" -ForegroundColor Yellow
    
    if (-not $Global:AuthToken) {
        Add-TestResult -TestName "User Endpoints" -Endpoint "/users/*" -Method "ALL" -Success $false -Message "No auth token available for testing"
        return
    }
    
    # Test get user profile
    $response = Invoke-ApiRequest -Method "GET" -Endpoint "/users/profile" -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "Get User Profile" -Endpoint "/users/profile" -Method "GET" -Success $true -Message "User profile retrieved successfully" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "Get User Profile" -Endpoint "/users/profile" -Method "GET" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }
    
    # Test update user profile
    $updateBody = @{
        name = "Updated Test User"
        telephone = "1234567890"
    }
    
    $response = Invoke-ApiRequest -Method "PUT" -Endpoint "/users/profile" -Body $updateBody -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "Update User Profile" -Endpoint "/users/profile" -Method "PUT" -Success $true -Message "User profile updated successfully" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "Update User Profile" -Endpoint "/users/profile" -Method "PUT" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }
    
    # Test password validation
    $passwordBody = @{
        password = $Global:TestUser.password
    }
    
    $response = Invoke-ApiRequest -Method "POST" -Endpoint "/users/validate-password" -Body $passwordBody -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "Validate Password" -Endpoint "/users/validate-password" -Method "POST" -Success $true -Message "Password validated successfully" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "Validate Password" -Endpoint "/users/validate-password" -Method "POST" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }
    
    # Test personal stats
    $response = Invoke-ApiRequest -Method "GET" -Endpoint "/users/personal-stats" -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "Get Personal Stats" -Endpoint "/users/personal-stats" -Method "GET" -Success $true -Message "Personal stats retrieved successfully" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "Get Personal Stats" -Endpoint "/users/personal-stats" -Method "GET" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }
    
    # Test profile detail
    $response = Invoke-ApiRequest -Method "GET" -Endpoint "/users/profile-detail" -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "Get Profile Detail" -Endpoint "/users/profile-detail" -Method "GET" -Success $true -Message "Profile detail retrieved successfully" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "Get Profile Detail" -Endpoint "/users/profile-detail" -Method "GET" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }
}

function Test-TeamEndpoints {
    Write-Host "`n=== Testing Team Management Endpoints ===" -ForegroundColor Yellow

    if (-not $Global:AuthToken) {
        Add-TestResult -TestName "Team Endpoints" -Endpoint "/teams/*" -Method "ALL" -Success $false -Message "No auth token available for testing"
        return
    }

    # Test create team
    $teamBody = @{
        name = "Test Team $(Get-Random)"
        description = "A test team created by API testing script"
    }

    $response = Invoke-ApiRequest -Method "POST" -Endpoint "/teams" -Body $teamBody -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "Create Team" -Endpoint "/teams" -Method "POST" -Success $true -Message "Team created successfully" -StatusCode $response.StatusCode
        $Global:TestTeamId = $response.Data.data.id
    } else {
        Add-TestResult -TestName "Create Team" -Endpoint "/teams" -Method "POST" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }

    # Test get teams list
    $response = Invoke-ApiRequest -Method "GET" -Endpoint "/teams" -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "Get Teams List" -Endpoint "/teams" -Method "GET" -Success $true -Message "Teams list retrieved successfully" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "Get Teams List" -Endpoint "/teams" -Method "GET" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }
}

function Test-SubscriptionEndpoints {
    Write-Host "`n=== Testing Subscription Management Endpoints ===" -ForegroundColor Yellow

    if (-not $Global:AuthToken) {
        Add-TestResult -TestName "Subscription Endpoints" -Endpoint "/subscriptions/*" -Method "ALL" -Success $false -Message "No auth token available for testing"
        return
    }

    # Test get user subscriptions
    $response = Invoke-ApiRequest -Method "GET" -Endpoint "/subscriptions" -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "Get User Subscriptions" -Endpoint "/subscriptions" -Method "GET" -Success $true -Message "User subscriptions retrieved successfully" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "Get User Subscriptions" -Endpoint "/subscriptions" -Method "GET" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }

    # Test get current subscription
    $response = Invoke-ApiRequest -Method "GET" -Endpoint "/subscriptions/current" -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "Get Current Subscription" -Endpoint "/subscriptions/current" -Method "GET" -Success $true -Message "Current subscription retrieved successfully" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "Get Current Subscription" -Endpoint "/subscriptions/current" -Method "GET" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }

    # Test create subscription (assuming plan ID 1 exists)
    $subscriptionBody = @{
        planId = 1
        paymentMethod = "CREDIT_CARD"
    }

    $response = Invoke-ApiRequest -Method "POST" -Endpoint "/subscriptions" -Body $subscriptionBody -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "Create Subscription" -Endpoint "/subscriptions" -Method "POST" -Success $true -Message "Subscription created successfully" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "Create Subscription" -Endpoint "/subscriptions" -Method "POST" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }
}

function Test-TodoEndpoints {
    Write-Host "`n=== Testing TODO Management Endpoints ===" -ForegroundColor Yellow

    if (-not $Global:AuthToken) {
        Add-TestResult -TestName "TODO Endpoints" -Endpoint "/todos/*" -Method "ALL" -Success $false -Message "No auth token available for testing"
        return
    }

    # Test get todos
    $response = Invoke-ApiRequest -Method "GET" -Endpoint "/todos" -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "Get TODOs" -Endpoint "/todos" -Method "GET" -Success $true -Message "TODOs retrieved successfully" -StatusCode $response.StatusCode
    } else {
        Add-TestResult -TestName "Get TODOs" -Endpoint "/todos" -Method "GET" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }

    # Test create todo
    $todoBody = @{
        title = "Test TODO $(Get-Random)"
        description = "A test TODO created by API testing script"
        priority = "MEDIUM"
        dueDate = (Get-Date).AddDays(7).ToString("yyyy-MM-dd")
    }

    $response = Invoke-ApiRequest -Method "POST" -Endpoint "/todos" -Body $todoBody -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "Create TODO" -Endpoint "/todos" -Method "POST" -Success $true -Message "TODO created successfully" -StatusCode $response.StatusCode
        $Global:TestTodoId = $response.Data.data.id
    } else {
        Add-TestResult -TestName "Create TODO" -Endpoint "/todos" -Method "POST" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }

    # Test update todo (if we have a todo ID)
    if ($Global:TestTodoId) {
        $updateTodoBody = @{
            title = "Updated Test TODO"
            description = "Updated description"
            status = "IN_PROGRESS"
        }

        $response = Invoke-ApiRequest -Method "PUT" -Endpoint "/todos/$Global:TestTodoId" -Body $updateTodoBody -RequireAuth $true
        if ($response.Success) {
            Add-TestResult -TestName "Update TODO" -Endpoint "/todos/$Global:TestTodoId" -Method "PUT" -Success $true -Message "TODO updated successfully" -StatusCode $response.StatusCode
        } else {
            Add-TestResult -TestName "Update TODO" -Endpoint "/todos/$Global:TestTodoId" -Method "PUT" -Success $false -Message $response.Error -StatusCode $response.StatusCode
        }
    }
}

function Test-LogoutEndpoint {
    Write-Host "`n=== Testing Logout Endpoint ===" -ForegroundColor Yellow

    if (-not $Global:AuthToken) {
        Add-TestResult -TestName "Logout" -Endpoint "/auth/logout" -Method "POST" -Success $false -Message "No auth token available for testing"
        return
    }

    # Test logout
    $response = Invoke-ApiRequest -Method "POST" -Endpoint "/auth/logout" -RequireAuth $true
    if ($response.Success) {
        Add-TestResult -TestName "User Logout" -Endpoint "/auth/logout" -Method "POST" -Success $true -Message "User logged out successfully" -StatusCode $response.StatusCode
        $Global:AuthToken = $null
    } else {
        Add-TestResult -TestName "User Logout" -Endpoint "/auth/logout" -Method "POST" -Success $false -Message $response.Error -StatusCode $response.StatusCode
    }
}

function New-TestReport {
    Write-Host "`n=== Test Results Summary ===" -ForegroundColor Yellow

    $totalTests = $Global:TestResults.Count
    $passedTests = ($Global:TestResults | Where-Object { $_.Success }).Count
    $failedTests = $totalTests - $passedTests
    $successRate = if ($totalTests -gt 0) { [math]::Round(($passedTests / $totalTests) * 100, 2) } else { 0 }

    Write-Host "`nOverall Results:" -ForegroundColor Cyan
    Write-Host "Total Tests: $totalTests" -ForegroundColor White
    Write-Host "Passed: $passedTests" -ForegroundColor Green
    Write-Host "Failed: $failedTests" -ForegroundColor Red
    Write-Host "Success Rate: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

    Write-Host "`nDetailed Results:" -ForegroundColor Cyan
    foreach ($result in $Global:TestResults) {
        $status = if ($result.Success) { "PASS" } else { "FAIL" }
        $color = if ($result.Success) { "Green" } else { "Red" }
        Write-Host "[$status] $($result.TestName) ($($result.Method) $($result.Endpoint))" -ForegroundColor $color
        if (!$result.Success) {
            Write-Host "  Error: $($result.Message)" -ForegroundColor Yellow
            Write-Host "  Status Code: $($result.StatusCode)" -ForegroundColor Yellow
        }
    }

    # Generate JSON report
    $reportPath = "api-test-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
    $reportData = @{
        TestSummary = @{
            TotalTests = $totalTests
            PassedTests = $passedTests
            FailedTests = $failedTests
            SuccessRate = $successRate
            TestDate = Get-Date
            BaseUrl = $BaseUrl
        }
        TestResults = $Global:TestResults
    }

    $reportData | ConvertTo-Json -Depth 10 | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Host "`nDetailed JSON report saved to: $reportPath" -ForegroundColor Green

    return @{
        TotalTests = $totalTests
        PassedTests = $passedTests
        FailedTests = $failedTests
        SuccessRate = $successRate
    }
}

# Main execution
function Main {
    Write-Host "Starting API Endpoint Testing for Team Management System" -ForegroundColor Green
    Write-Host "Base URL: $BaseUrl" -ForegroundColor Cyan
    Write-Host "Verbose Mode: $Verbose" -ForegroundColor Cyan
    Write-Host "Test User Email: $($Global:TestUser.email)" -ForegroundColor Cyan

    # Check if backend is running
    try {
        Invoke-RestMethod -Uri "$BaseUrl/actuator/health" -Method GET -UseBasicParsing -ErrorAction SilentlyContinue | Out-Null
        Write-Host "Backend health check: OK" -ForegroundColor Green
    }
    catch {
        try {
            # Try a simple endpoint to see if server is running
            Invoke-RestMethod -Uri "$BaseUrl/plans" -Method GET -UseBasicParsing -ErrorAction SilentlyContinue | Out-Null
            Write-Host "Backend is running (health endpoint not available)" -ForegroundColor Yellow
        }
        catch {
            Write-Host "WARNING: Backend may not be running or accessible at $BaseUrl" -ForegroundColor Red
            Write-Host "Continuing with tests anyway..." -ForegroundColor Yellow
        }
    }

    # Run all tests
    Test-PublicEndpoints
    $authSuccess = Test-AuthenticationFlow

    if ($authSuccess) {
        Test-UserEndpoints
        Test-TeamEndpoints
        Test-SubscriptionEndpoints
        Test-TodoEndpoints
        Test-LogoutEndpoint
    } else {
        Write-Host "`nSkipping authenticated endpoint tests due to authentication failure" -ForegroundColor Yellow
    }

    # Generate and display report
    $summary = New-TestReport

    # Exit with appropriate code
    if ($summary.FailedTests -eq 0) {
        Write-Host "`nAll tests passed! ✅" -ForegroundColor Green
        exit 0
    } else {
        Write-Host "`nSome tests failed! ❌" -ForegroundColor Red
        exit 1
    }
}

# Run the main function
Main
