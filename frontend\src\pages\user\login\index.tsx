/**
 * 登录页面
 * 实现双阶段认证的第一阶段：账号登录
 */

import { LockOutlined, MailOutlined, UserOutlined } from '@ant-design/icons';
import { Helmet, history, useModel } from '@umijs/max';
import {
  Button,
  Card,
  Form,
  Input,
  message,
  Space,
  Tabs,
  Typography,
} from 'antd';
import { createStyles } from 'antd-style';
import React, { useState } from 'react';
import { Footer } from '@/components';
import { AuthService } from '@/services';
import type { LoginRequest, RegisterRequest } from '@/types/api';
import Settings from '../../../../config/defaultSettings';

const { Title, Text } = Typography;

const useStyles = createStyles(({ token }) => {
  return {
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
      backgroundSize: '100% 100%',
    },
    content: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '32px 16px',
    },
    header: {
      marginBottom: 40,
      textAlign: 'center',
    },
    logo: {
      marginBottom: 16,
    },
    title: {
      marginBottom: 0,
    },
    loginCard: {
      width: '100%',
      maxWidth: 400,
      boxShadow: token.boxShadowTertiary,
    },
    footer: {
      marginTop: 40,
      textAlign: 'center',
    },
    lang: {
      width: 42,
      height: 42,
      lineHeight: '42px',
      position: 'fixed',
      right: 16,
      top: 16,
      borderRadius: token.borderRadius,
      ':hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
  };
});

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('login');
  const { styles } = useStyles();
  const { setInitialState } = useModel('@@initialState');

  // 自定义邮箱验证函数
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // 处理登录
  const handleLogin = async (values: LoginRequest) => {
    setLoading(true);
    try {
      const response = await AuthService.login(values);
      message.success('登录成功！');

      // 登录成功后，刷新 initialState
      await setInitialState((prevState) => ({
        ...prevState,
        currentUser: response.user,
        currentTeam: response.teams.length > 0 ? response.teams[0] : undefined,
      }));

      // 根据团队数量进行不同的跳转处理
      if (response.teams.length === 0) {
        // 没有团队，跳转到创建团队页面
        history.push('/team/create');
      } else {
        // 有团队（无论一个还是多个），都跳转到个人中心整合页面
        history.push('/personal-center', { teams: response.teams });
      }
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理注册
  const handleRegister = async (values: RegisterRequest) => {
    setLoading(true);
    try {
      await AuthService.register(values);
      message.success('注册成功！请登录');
      setActiveTab('login');
    } catch (error) {
      console.error('注册失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 登录表单
  const LoginForm = () => (
    <Form name="login" size="large" onFinish={handleLogin} autoComplete="off">
      <Form.Item
        name="email"
        rules={[
          { required: true, message: '请输入邮箱！' },
          {
            validator: (_, value) => {
              if (!value || validateEmail(value)) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('请输入有效的邮箱地址！'));
            },
          },
        ]}
      >
        <Input
          prefix={<MailOutlined />}
          placeholder="邮箱"
          autoComplete="email"
        />
      </Form.Item>

      <Form.Item
        name="password"
        rules={[{ required: true, message: '请输入密码！' }]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="密码"
          autoComplete="current-password"
        />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading} block>
          登录
        </Button>
      </Form.Item>
    </Form>
  );

  // 注册表单
  const RegisterForm = () => (
    <Form
      name="register"
      size="large"
      onFinish={handleRegister}
      autoComplete="off"
    >
      <Form.Item
        name="name"
        rules={[
          { required: true, message: '请输入用户名！' },
          { max: 100, message: '用户名长度不能超过100字符！' },
        ]}
      >
        <Input
          prefix={<UserOutlined />}
          placeholder="用户名"
          autoComplete="name"
        />
      </Form.Item>

      <Form.Item
        name="email"
        rules={[
          { required: true, message: '请输入邮箱！' },
          {
            validator: (_, value) => {
              if (!value || validateEmail(value)) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('请输入有效的邮箱地址！'));
            },
          },
        ]}
      >
        <Input
          prefix={<MailOutlined />}
          placeholder="邮箱"
          autoComplete="email"
        />
      </Form.Item>

      <Form.Item
        name="password"
        rules={[
          { required: true, message: '请输入密码！' },
          { min: 8, message: '密码长度至少8位！' },
        ]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="密码（至少8位）"
          autoComplete="new-password"
        />
      </Form.Item>

      <Form.Item
        name="confirmPassword"
        dependencies={['password']}
        rules={[
          { required: true, message: '请确认密码！' },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('password') === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('两次输入的密码不一致！'));
            },
          }),
        ]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="确认密码"
          autoComplete="new-password"
        />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading} block>
          注册
        </Button>
      </Form.Item>
    </Form>
  );

  const tabItems = [
    {
      key: 'login',
      label: '登录',
      children: <LoginForm />,
    },
    {
      key: 'register',
      label: '注册',
      children: <RegisterForm />,
    },
  ];

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          登录页
          {Settings.title && ` - ${Settings.title}`}
        </title>
      </Helmet>
      <div className={styles.content}>
        <div className={styles.header}>
          <Space direction="vertical" align="center" size="large">
            <div className={styles.logo}>
              <img src="/logo.svg" alt="TeamAuth" height={48} />
            </div>
            <div className={styles.title}>
              <Title level={2}>团队管理系统</Title>
              <Text type="secondary">现代化的团队协作与管理平台</Text>
            </div>
          </Space>
        </div>

        <Card className={styles.loginCard}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            centered
            items={tabItems}
          />
        </Card>

        <div className={styles.footer}>
          <Text type="secondary">© 2025 TeamAuth. All rights reserved.</Text>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default LoginPage;
