{"version": 3, "sources": ["src/components/InvitationStatus.tsx", "src/services/invitation.ts", "src/utils/teamSelectionUtils.ts"], "sourcesContent": ["/**\n * 邀请状态显示组件\n */\n\nimport React from 'react';\nimport { Tag } from 'antd';\nimport {\n  ClockCircleOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  ExclamationCircleOutlined,\n  StopOutlined,\n} from '@ant-design/icons';\nimport { InvitationStatus } from '@/types/api';\n\ninterface InvitationStatusProps {\n  status: InvitationStatus;\n  isExpired?: boolean;\n}\n\n/**\n * 邀请状态组件\n */\nconst InvitationStatusComponent: React.FC<InvitationStatusProps> = ({ \n  status, \n  isExpired = false \n}) => {\n  // 如果已过期，优先显示过期状态\n  if (isExpired && status === InvitationStatus.PENDING) {\n    return (\n      <Tag icon={<ExclamationCircleOutlined />} color=\"orange\">\n        已过期\n      </Tag>\n    );\n  }\n\n  switch (status) {\n    case InvitationStatus.PENDING:\n      return (\n        <Tag icon={<ClockCircleOutlined />} color=\"blue\">\n          待确认\n        </Tag>\n      );\n    case InvitationStatus.ACCEPTED:\n      return (\n        <Tag icon={<CheckCircleOutlined />} color=\"green\">\n          已接受\n        </Tag>\n      );\n    case InvitationStatus.REJECTED:\n      return (\n        <Tag icon={<CloseCircleOutlined />} color=\"red\">\n          已拒绝\n        </Tag>\n      );\n    case InvitationStatus.EXPIRED:\n      return (\n        <Tag icon={<ExclamationCircleOutlined />} color=\"orange\">\n          已过期\n        </Tag>\n      );\n    case InvitationStatus.CANCELLED:\n      return (\n        <Tag icon={<StopOutlined />} color=\"default\">\n          已取消\n        </Tag>\n      );\n    default:\n      return (\n        <Tag color=\"default\">\n          未知状态\n        </Tag>\n      );\n  }\n};\n\nexport default InvitationStatusComponent;\n", "/**\n * 团队邀请相关 API 服务\n */\n\nimport type {\n  TeamInvitationResponse,\n  RespondInvitationRequest,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\n\n/**\n * 邀请服务类\n */\nexport class InvitationService {\n  /**\n   * 获取当前团队的邀请列表（需要 Team Token，仅创建者）\n   */\n  static async getCurrentTeamInvitations(): Promise<TeamInvitationResponse[]> {\n    const response = await apiRequest.get<TeamInvitationResponse[]>('/teams/current/invitations');\n    return response.data;\n  }\n\n  /**\n   * 获取用户收到的邀请列表（需要 Account Token）\n   */\n  static async getUserReceivedInvitations(): Promise<TeamInvitationResponse[]> {\n    const response = await apiRequest.get<TeamInvitationResponse[]>('/invitations/user/received');\n    return response.data;\n  }\n\n  /**\n   * 获取用户收到的待处理邀请列表（需要 Account Token）\n   */\n  static async getUserPendingInvitations(): Promise<TeamInvitationResponse[]> {\n    const response = await apiRequest.get<TeamInvitationResponse[]>('/invitations/user/pending');\n    return response.data;\n  }\n\n  /**\n   * 响应邀请（需要 Account Token）\n   */\n  static async respondToInvitation(\n    invitationId: number,\n    data: RespondInvitationRequest,\n  ): Promise<void> {\n    await apiRequest.post<void>(`/invitations/${invitationId}/respond`, data);\n  }\n\n  /**\n   * 取消邀请（需要 Team Token，仅邀请人）\n   */\n  static async cancelInvitation(invitationId: number): Promise<void> {\n    await apiRequest.delete<void>(`/invitations/${invitationId}`);\n  }\n\n  /**\n   * 获取邀请详情\n   */\n  static async getInvitationDetail(invitationId: number): Promise<TeamInvitationResponse> {\n    const response = await apiRequest.get<TeamInvitationResponse>(`/invitations/${invitationId}`);\n    return response.data;\n  }\n\n  /**\n   * 更新过期邀请状态（系统内部接口）\n   */\n  static async updateExpiredInvitations(): Promise<number> {\n    const response = await apiRequest.post<number>('/invitations/system/update-expired');\n    return response.data;\n  }\n}\n", "/**\n * 团队选择状态管理工具函数\n * 用于跟踪用户是否已经主动选择过团队，以区分初始登录状态和主动选择状态\n */\n\n// 团队选择历史的本地存储键\nconst TEAM_SELECTION_KEY = 'user_team_selection_history';\n\n/**\n * 获取用户的团队选择历史\n * @param userId 用户ID\n * @returns 用户选择过的团队ID集合\n */\nexport const getUserTeamSelectionHistory = (userId: number): Set<number> => {\n  try {\n    const history = localStorage.getItem(`${TEAM_SELECTION_KEY}_${userId}`);\n    if (history) {\n      return new Set(JSON.parse(history));\n    }\n  } catch (error) {\n    console.error('获取团队选择历史失败:', error);\n  }\n  return new Set();\n};\n\n/**\n * 记录用户选择了某个团队\n * @param userId 用户ID\n * @param teamId 团队ID\n */\nexport const recordTeamSelection = (userId: number, teamId: number): void => {\n  try {\n    const history = getUserTeamSelectionHistory(userId);\n    history.add(teamId);\n    localStorage.setItem(`${TEAM_SELECTION_KEY}_${userId}`, JSON.stringify([...history]));\n    console.log(`记录团队选择: 用户${userId}选择了团队${teamId}`);\n  } catch (error) {\n    console.error('记录团队选择历史失败:', error);\n  }\n};\n\n/**\n * 检查用户是否曾经选择过某个团队\n * @param userId 用户ID\n * @param teamId 团队ID\n * @returns 是否曾经选择过该团队\n */\nexport const hasUserSelectedTeam = (userId: number, teamId: number): boolean => {\n  const history = getUserTeamSelectionHistory(userId);\n  return history.has(teamId);\n};\n\n/**\n * 清除用户的团队选择历史（用于注销等场景）\n * @param userId 用户ID\n */\nexport const clearUserTeamSelectionHistory = (userId: number): void => {\n  try {\n    localStorage.removeItem(`${TEAM_SELECTION_KEY}_${userId}`);\n    console.log(`清除用户${userId}的团队选择历史`);\n  } catch (error) {\n    console.error('清除团队选择历史失败:', error);\n  }\n};\n\n/**\n * 获取所有用户的团队选择历史键（用于调试）\n * @returns 所有相关的localStorage键\n */\nexport const getAllTeamSelectionKeys = (): string[] => {\n  const keys: string[] = [];\n  for (let i = 0; i < localStorage.length; i++) {\n    const key = localStorage.key(i);\n    if (key && key.startsWith(TEAM_SELECTION_KEY)) {\n      keys.push(key);\n    }\n  }\n  return keys;\n};\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BA0ED;;;eAAA;;;;;;;uEAxEkB;6BACE;8BAOb;4BAC0B;;;;;;;;;AAOjC;;CAEC,GACD,MAAM,4BAA6D,CAAC,EAClE,MAAM,EACN,YAAY,KAAK,EAClB;IACC,iBAAiB;IACjB,IAAI,aAAa,WAAW,qBAAgB,CAAC,OAAO,EAClD,qBACE,2BAAC,SAAG;QAAC,oBAAM,2BAAC,gCAAyB;;;;;QAAK,OAAM;kBAAS;;;;;;IAM7D,OAAQ;QACN,KAAK,qBAAgB,CAAC,OAAO;YAC3B,qBACE,2BAAC,SAAG;gBAAC,oBAAM,2BAAC,0BAAmB;;;;;gBAAK,OAAM;0BAAO;;;;;;QAIrD,KAAK,qBAAgB,CAAC,QAAQ;YAC5B,qBACE,2BAAC,SAAG;gBAAC,oBAAM,2BAAC,0BAAmB;;;;;gBAAK,OAAM;0BAAQ;;;;;;QAItD,KAAK,qBAAgB,CAAC,QAAQ;YAC5B,qBACE,2BAAC,SAAG;gBAAC,oBAAM,2BAAC,0BAAmB;;;;;gBAAK,OAAM;0BAAM;;;;;;QAIpD,KAAK,qBAAgB,CAAC,OAAO;YAC3B,qBACE,2BAAC,SAAG;gBAAC,oBAAM,2BAAC,gCAAyB;;;;;gBAAK,OAAM;0BAAS;;;;;;QAI7D,KAAK,qBAAgB,CAAC,SAAS;YAC7B,qBACE,2BAAC,SAAG;gBAAC,oBAAM,2BAAC,mBAAY;;;;;gBAAK,OAAM;0BAAU;;;;;;QAIjD;YACE,qBACE,2BAAC,SAAG;gBAAC,OAAM;0BAAU;;;;;;IAI3B;AACF;KAnDM;IAqDN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5Ef;;CAEC;;;;4BAWY;;;eAAA;;;;;gCALc;;;;;;;;;AAKpB,MAAM;IACX;;GAEC,GACD,aAAa,4BAA+D;QAC1E,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAA2B;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,6BAAgE;QAC3E,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAA2B;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,4BAA+D;QAC1E,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAA2B;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,oBACX,YAAoB,EACpB,IAA8B,EACf;QACf,MAAM,mBAAU,CAAC,IAAI,CAAO,CAAC,aAAa,EAAE,aAAa,QAAQ,CAAC,EAAE;IACtE;IAEA;;GAEC,GACD,aAAa,iBAAiB,YAAoB,EAAiB;QACjE,MAAM,mBAAU,CAAC,MAAM,CAAO,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9D;IAEA;;GAEC,GACD,aAAa,oBAAoB,YAAoB,EAAmC;QACtF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAyB,CAAC,aAAa,EAAE,aAAa,CAAC;QAC5F,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,2BAA4C;QACvD,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAS;QAC/C,OAAO,SAAS,IAAI;IACtB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;ACtEA;;;CAGC,GAED,eAAe;;;;;;;;;;;;IAmDF,6BAA6B;eAA7B;;IAaA,uBAAuB;eAAvB;;IAxDA,2BAA2B;eAA3B;;IAkCA,mBAAmB;eAAnB;;IAjBA,mBAAmB;eAAnB;;;;;;;;;;;;;AAxBb,MAAM,qBAAqB;AAOpB,MAAM,8BAA8B,CAAC;IAC1C,IAAI;QACF,MAAM,UAAU,aAAa,OAAO,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC;QACtE,IAAI,SACF,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC;IAE9B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;IAC/B;IACA,OAAO,IAAI;AACb;AAOO,MAAM,sBAAsB,CAAC,QAAgB;IAClD,IAAI;QACF,MAAM,UAAU,4BAA4B;QAC5C,QAAQ,GAAG,CAAC;QACZ,aAAa,OAAO,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,SAAS,CAAC;eAAI;SAAQ;QACnF,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,KAAK,EAAE,OAAO,CAAC;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;IAC/B;AACF;AAQO,MAAM,sBAAsB,CAAC,QAAgB;IAClD,MAAM,UAAU,4BAA4B;IAC5C,OAAO,QAAQ,GAAG,CAAC;AACrB;AAMO,MAAM,gCAAgC,CAAC;IAC5C,IAAI;QACF,aAAa,UAAU,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC;QACzD,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,OAAO,CAAC;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;IAC/B;AACF;AAMO,MAAM,0BAA0B;IACrC,MAAM,OAAiB,EAAE;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;QAC7B,IAAI,OAAO,IAAI,UAAU,CAAC,qBACxB,KAAK,IAAI,CAAC;IAEd;IACA,OAAO;AACT"}