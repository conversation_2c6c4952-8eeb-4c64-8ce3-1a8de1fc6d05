((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['p__user__invitations__index'],
{ "src/pages/user/invitations/index.tsx": function (module, exports, __mako_require__){
/**
 * 用户邀请页面
 * 
 * 功能特性：
 * - 显示用户收到的所有邀请
 * - 支持接受或拒绝邀请
 * - 显示邀请详情和团队信息
 * - 自动刷新待处理邀请
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _dayjs = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/dayjs/dayjs.min.js"));
var _invitation = __mako_require__("src/services/invitation.ts");
var _api = __mako_require__("src/types/api.ts");
var _InvitationStatus = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/InvitationStatus.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text, Title } = _antd.Typography;
const { TextArea } = _antd.Input;
const UserInvitationsPage = ()=>{
    _s();
    const [invitations, setInvitations] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(false);
    const [respondModalVisible, setRespondModalVisible] = (0, _react.useState)(false);
    const [selectedInvitation, setSelectedInvitation] = (0, _react.useState)(null);
    const [respondForm] = _antd.Form.useForm();
    // 获取邀请列表
    const fetchInvitations = async ()=>{
        try {
            setLoading(true);
            const invitationList = await _invitation.InvitationService.getUserReceivedInvitations();
            setInvitations(invitationList || []);
        } catch (error) {
            console.error('获取邀请列表失败:', error);
            _antd.message.error('获取邀请列表失败');
            setInvitations([]);
        } finally{
            setLoading(false);
        }
    };
    (0, _react.useEffect)(()=>{
        fetchInvitations();
    }, []);
    // 响应邀请
    const handleRespondInvitation = async (invitation, accept)=>{
        setSelectedInvitation(invitation);
        respondForm.setFieldsValue({
            accept
        });
        setRespondModalVisible(true);
    };
    // 提交响应
    const handleSubmitResponse = async (values)=>{
        if (!selectedInvitation) return;
        try {
            const request = {
                accept: values.accept,
                message: values.message
            };
            await _invitation.InvitationService.respondToInvitation(selectedInvitation.id, request);
            const action = values.accept ? '接受' : '拒绝';
            _antd.message.success(`邀请${action}成功`);
            setRespondModalVisible(false);
            respondForm.resetFields();
            setSelectedInvitation(null);
            fetchInvitations();
        } catch (error) {
            console.error('响应邀请失败:', error);
            _antd.message.error('响应邀请失败');
        }
    };
    // 表格列定义
    const columns = [
        {
            title: '团队信息',
            key: 'team',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/invitations/index.tsx",
                                lineNumber: 111,
                                columnNumber: 25
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/user/invitations/index.tsx",
                            lineNumber: 111,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: record.teamName
                                }, void 0, false, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 113,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 114,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    style: {
                                        fontSize: '12px'
                                    },
                                    children: [
                                        "团队ID: ",
                                        record.teamId
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 115,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/invitations/index.tsx",
                            lineNumber: 112,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/invitations/index.tsx",
                    lineNumber: 110,
                    columnNumber: 9
                }, this)
        },
        {
            title: '邀请人',
            key: 'inviter',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/invitations/index.tsx",
                                lineNumber: 127,
                                columnNumber: 25
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/user/invitations/index.tsx",
                            lineNumber: 127,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: record.inviterName
                                }, void 0, false, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 129,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 130,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    style: {
                                        fontSize: '12px'
                                    },
                                    children: record.inviterEmail
                                }, void 0, false, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 131,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/invitations/index.tsx",
                            lineNumber: 128,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/invitations/index.tsx",
                    lineNumber: 126,
                    columnNumber: 9
                }, this)
        },
        {
            title: '邀请状态',
            dataIndex: 'status',
            key: 'status',
            render: (status, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_InvitationStatus.default, {
                    status: status,
                    isExpired: record.isExpired
                }, void 0, false, {
                    fileName: "src/pages/user/invitations/index.tsx",
                    lineNumber: 143,
                    columnNumber: 9
                }, this)
        },
        {
            title: '邀请时间',
            dataIndex: 'invitedAt',
            key: 'invitedAt',
            render: (time)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                    title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                    children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                }, void 0, false, {
                    fileName: "src/pages/user/invitations/index.tsx",
                    lineNumber: 154,
                    columnNumber: 9
                }, this),
            sorter: (a, b)=>(0, _dayjs.default)(a.invitedAt).unix() - (0, _dayjs.default)(b.invitedAt).unix(),
            defaultSortOrder: 'descend'
        },
        {
            title: '过期时间',
            dataIndex: 'expiresAt',
            key: 'expiresAt',
            render: (time, record)=>{
                const isExpired = record.isExpired;
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                    title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                        type: isExpired ? 'danger' : 'secondary',
                        children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                    }, void 0, false, {
                        fileName: "src/pages/user/invitations/index.tsx",
                        lineNumber: 169,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/user/invitations/index.tsx",
                    lineNumber: 168,
                    columnNumber: 11
                }, this);
            }
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: record.canBeResponded && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {}, void 0, false, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 186,
                                    columnNumber: 23
                                }, void 0),
                                onClick: ()=>handleRespondInvitation(record, true),
                                children: "接受"
                            }, void 0, false, {
                                fileName: "src/pages/user/invitations/index.tsx",
                                lineNumber: 183,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CloseOutlined, {}, void 0, false, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 193,
                                    columnNumber: 23
                                }, void 0),
                                onClick: ()=>handleRespondInvitation(record, false),
                                children: "拒绝"
                            }, void 0, false, {
                                fileName: "src/pages/user/invitations/index.tsx",
                                lineNumber: 191,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "src/pages/user/invitations/index.tsx",
                    lineNumber: 180,
                    columnNumber: 9
                }, this)
        }
    ];
    // 统计信息
    const pendingCount = invitations.filter((inv)=>inv.status === _api.InvitationStatus.PENDING && !inv.isExpired).length;
    const acceptedCount = invitations.filter((inv)=>inv.status === _api.InvitationStatus.ACCEPTED).length;
    const rejectedCount = invitations.filter((inv)=>inv.status === _api.InvitationStatus.REJECTED).length;
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "我的邀请",
        subTitle: "管理您收到的团队邀请",
        extra: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ReloadOutlined, {}, void 0, false, {
                    fileName: "src/pages/user/invitations/index.tsx",
                    lineNumber: 217,
                    columnNumber: 17
                }, void 0),
                onClick: fetchInvitations,
                loading: loading,
                children: "刷新"
            }, "refresh", false, {
                fileName: "src/pages/user/invitations/index.tsx",
                lineNumber: 215,
                columnNumber: 9
            }, void 0)
        ],
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                direction: "vertical",
                size: "large",
                style: {
                    width: '100%'
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            size: "large",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: "待处理邀请"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/invitations/index.tsx",
                                            lineNumber: 230,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                            fileName: "src/pages/user/invitations/index.tsx",
                                            lineNumber: 231,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            style: {
                                                fontSize: '24px',
                                                fontWeight: 'bold',
                                                color: '#1890ff'
                                            },
                                            children: pendingCount
                                        }, void 0, false, {
                                            fileName: "src/pages/user/invitations/index.tsx",
                                            lineNumber: 232,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 229,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: "已接受"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/invitations/index.tsx",
                                            lineNumber: 237,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                            fileName: "src/pages/user/invitations/index.tsx",
                                            lineNumber: 238,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            style: {
                                                fontSize: '24px',
                                                fontWeight: 'bold',
                                                color: '#52c41a'
                                            },
                                            children: acceptedCount
                                        }, void 0, false, {
                                            fileName: "src/pages/user/invitations/index.tsx",
                                            lineNumber: 239,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 236,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: "已拒绝"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/invitations/index.tsx",
                                            lineNumber: 244,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                            fileName: "src/pages/user/invitations/index.tsx",
                                            lineNumber: 245,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            style: {
                                                fontSize: '24px',
                                                fontWeight: 'bold',
                                                color: '#ff4d4f'
                                            },
                                            children: rejectedCount
                                        }, void 0, false, {
                                            fileName: "src/pages/user/invitations/index.tsx",
                                            lineNumber: 246,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 243,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/invitations/index.tsx",
                            lineNumber: 228,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/user/invitations/index.tsx",
                        lineNumber: 227,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        title: "邀请列表",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                            columns: columns,
                            dataSource: invitations,
                            rowKey: "id",
                            loading: loading,
                            pagination: {
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total)=>`共 ${total} 条邀请记录`,
                                pageSize: 10
                            }
                        }, void 0, false, {
                            fileName: "src/pages/user/invitations/index.tsx",
                            lineNumber: 255,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/user/invitations/index.tsx",
                        lineNumber: 254,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/user/invitations/index.tsx",
                lineNumber: 226,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: `${respondForm.getFieldValue('accept') ? '接受' : '拒绝'}邀请`,
                open: respondModalVisible,
                onCancel: ()=>{
                    setRespondModalVisible(false);
                    respondForm.resetFields();
                    setSelectedInvitation(null);
                },
                footer: null,
                width: 500,
                children: [
                    selectedInvitation && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            marginBottom: 16
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                strong: true,
                                children: "团队："
                            }, void 0, false, {
                                fileName: "src/pages/user/invitations/index.tsx",
                                lineNumber: 284,
                                columnNumber: 13
                            }, this),
                            " ",
                            selectedInvitation.teamName,
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                fileName: "src/pages/user/invitations/index.tsx",
                                lineNumber: 285,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                strong: true,
                                children: "邀请人："
                            }, void 0, false, {
                                fileName: "src/pages/user/invitations/index.tsx",
                                lineNumber: 286,
                                columnNumber: 13
                            }, this),
                            " ",
                            selectedInvitation.inviterName,
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                fileName: "src/pages/user/invitations/index.tsx",
                                lineNumber: 287,
                                columnNumber: 13
                            }, this),
                            selectedInvitation.message && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "邀请消息："
                                    }, void 0, false, {
                                        fileName: "src/pages/user/invitations/index.tsx",
                                        lineNumber: 290,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            background: '#f5f5f5',
                                            padding: 8,
                                            borderRadius: 4,
                                            marginTop: 4
                                        },
                                        children: selectedInvitation.message
                                    }, void 0, false, {
                                        fileName: "src/pages/user/invitations/index.tsx",
                                        lineNumber: 291,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/invitations/index.tsx",
                        lineNumber: 283,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                        form: respondForm,
                        layout: "vertical",
                        onFinish: handleSubmitResponse,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "accept",
                                hidden: true,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {}, void 0, false, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 310,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/user/invitations/index.tsx",
                                lineNumber: 309,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "message",
                                label: "回复消息（可选）",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                    rows: 3,
                                    placeholder: "您可以添加一些回复消息...",
                                    maxLength: 200
                                }, void 0, false, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 317,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/user/invitations/index.tsx",
                                lineNumber: 313,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            htmlType: "submit",
                                            icon: respondForm.getFieldValue('accept') ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {}, void 0, false, {
                                                fileName: "src/pages/user/invitations/index.tsx",
                                                lineNumber: 329,
                                                columnNumber: 61
                                            }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CloseOutlined, {}, void 0, false, {
                                                fileName: "src/pages/user/invitations/index.tsx",
                                                lineNumber: 329,
                                                columnNumber: 81
                                            }, void 0),
                                            children: [
                                                "确认",
                                                respondForm.getFieldValue('accept') ? '接受' : '拒绝'
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/user/invitations/index.tsx",
                                            lineNumber: 326,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            onClick: ()=>setRespondModalVisible(false),
                                            children: "取消"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/invitations/index.tsx",
                                            lineNumber: 333,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/invitations/index.tsx",
                                    lineNumber: 325,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/user/invitations/index.tsx",
                                lineNumber: 324,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/invitations/index.tsx",
                        lineNumber: 304,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/user/invitations/index.tsx",
                lineNumber: 271,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/user/invitations/index.tsx",
        lineNumber: 211,
        columnNumber: 5
    }, this);
};
_s(UserInvitationsPage, "qPSLqzJFiRvITigMwkbFbKp0uRI=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = UserInvitationsPage;
var _default = UserInvitationsPage;
var _c;
$RefreshReg$(_c, "UserInvitationsPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__user__invitations__index-async.js.map